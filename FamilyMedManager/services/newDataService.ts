import { SQLiteDataService } from './sqliteDataService';
import { MigrationService } from './migrationService';
import { FamilyMember, Medication } from '../types/medication';

/**
 * New DataService that uses SQLite as the primary storage
 * This maintains the same API as the old DataService for backward compatibility
 * while providing better performance and data persistence
 */
export class DataService {
  private static initialized = false;

  /**
   * Initialize the data service with automatic migration from old storage
   */
  static async initializeData(): Promise<void> {
    try {
      if (this.initialized) {
        return;
      }

      console.log('Initializing new DataService with SQLite...');

      // Perform migration from old storage if needed
      const migrationSuccess = await MigrationService.migrateToSQLite();
      
      if (!migrationSuccess) {
        console.warn('Migration failed, but continuing with SQLite initialization');
      }

      // Ensure SQLite is initialized
      await SQLiteDataService.initializeData();
      
      this.initialized = true;
      console.log('New DataService initialized successfully');
    } catch (error) {
      console.error('Error initializing new DataService:', error);
      throw error;
    }
  }

  /**
   * Get all family members
   */
  static async getFamilyMembers(): Promise<FamilyMember[]> {
    await this.ensureInitialized();
    return SQLiteDataService.getFamilyMembers();
  }

  /**
   * Get all medications
   */
  static async getMedications(): Promise<Medication[]> {
    await this.ensureInitialized();
    return SQLiteDataService.getMedications();
  }

  /**
   * Save family members
   */
  static async saveFamilyMembers(members: FamilyMember[]): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.saveFamilyMembers(members);
  }

  /**
   * Save medications
   */
  static async saveMedications(medications: Medication[]): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.saveMedications(medications);
  }

  /**
   * Add a new family member
   */
  static async addFamilyMember(member: FamilyMember): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.addFamilyMember(member);
  }

  /**
   * Update a family member
   */
  static async updateFamilyMember(member: FamilyMember): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.updateFamilyMember(member);
  }

  /**
   * Delete a family member
   */
  static async deleteFamilyMember(memberId: string): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.deleteFamilyMember(memberId);
  }

  /**
   * Add a new medication
   */
  static async addMedication(medication: Medication): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.addMedication(medication);
  }

  /**
   * Update a medication
   */
  static async updateMedication(medication: Medication): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.updateMedication(medication);
  }

  /**
   * Delete a medication
   */
  static async deleteMedication(medicationId: string): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.deleteMedication(medicationId);
  }

  /**
   * Record a dose taken
   */
  static async takeDose(medicationId: string): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.takeDose(medicationId);
  }

  /**
   * Clear all data
   */
  static async clearAllData(): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.clearAllData();
  }

  /**
   * Debug storage
   */
  static async debugStorage(): Promise<void> {
    await this.ensureInitialized();
    return SQLiteDataService.debugStorage();
  }

  /**
   * Get storage statistics
   */
  static async getStorageStats(): Promise<{
    familyMembersCount: number;
    medicationsCount: number;
    assignmentsCount: number;
    databaseSize?: number;
  }> {
    await this.ensureInitialized();
    return SQLiteDataService.getStorageStats();
  }

  /**
   * Backup data
   */
  static async backupData(): Promise<{
    familyMembers: FamilyMember[];
    medications: Medication[];
    timestamp: string;
  } | null> {
    await this.ensureInitialized();
    return MigrationService.backupSQLiteData();
  }

  /**
   * Restore data from backup
   */
  static async restoreFromBackup(backup: {
    familyMembers: FamilyMember[];
    medications: Medication[];
    timestamp: string;
  }): Promise<boolean> {
    await this.ensureInitialized();
    return MigrationService.restoreFromBackup(backup);
  }

  /**
   * Get migration status
   */
  static isMigrationCompleted(): boolean {
    return MigrationService.isMigrationCompleted();
  }

  /**
   * Compare old and new storage systems
   */
  static async compareStorageSystems(): Promise<{
    oldStorage: { familyMembers: number; medications: number };
    newStorage: { familyMembers: number; medications: number; assignments: number };
    match: boolean;
  }> {
    return MigrationService.compareStorageSystems();
  }

  /**
   * Ensure the service is initialized
   */
  private static async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initializeData();
    }
  }

  // Legacy methods for backward compatibility
  
  /**
   * @deprecated Use addFamilyMember instead
   */
  static async createFamilyMember(member: FamilyMember): Promise<void> {
    console.warn('createFamilyMember is deprecated, use addFamilyMember instead');
    return this.addFamilyMember(member);
  }

  /**
   * @deprecated Use addMedication instead
   */
  static async createMedication(medication: Medication): Promise<void> {
    console.warn('createMedication is deprecated, use addMedication instead');
    return this.addMedication(medication);
  }

  /**
   * Get a single family member by ID
   */
  static async getFamilyMember(memberId: string): Promise<FamilyMember | null> {
    const members = await this.getFamilyMembers();
    return members.find(member => member.id === memberId) || null;
  }

  /**
   * Get a single medication by ID
   */
  static async getMedication(medicationId: string): Promise<Medication | null> {
    const medications = await this.getMedications();
    return medications.find(medication => medication.id === medicationId) || null;
  }

  /**
   * Get medications assigned to a specific family member
   */
  static async getMedicationsForMember(memberId: string): Promise<Medication[]> {
    const medications = await this.getMedications();
    return medications.filter(medication => 
      medication.assignedMembers.includes(memberId)
    );
  }

  /**
   * Get medications with low stock
   */
  static async getLowStockMedications(): Promise<Medication[]> {
    const medications = await this.getMedications();
    return medications.filter(medication => 
      medication.stockLevel === 'low' || medication.stockLevel === 'critical'
    );
  }

  /**
   * Get medications that need refill soon
   */
  static async getMedicationsNeedingRefill(daysThreshold: number = 7): Promise<Medication[]> {
    const medications = await this.getMedications();
    return medications.filter(medication => 
      medication.daysLeft <= daysThreshold
    );
  }
}
