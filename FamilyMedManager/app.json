{"expo": {"name": "FamilyMedManager", "slug": "FamilyMedManager", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "familymedmanager", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"backgroundColor": "#E6F4FE", "foregroundImage": "./assets/images/android-icon-foreground.png", "backgroundImage": "./assets/images/android-icon-background.png", "monochromeImage": "./assets/images/android-icon-monochrome.png"}, "edgeToEdgeEnabled": true, "predictiveBackGestureEnabled": false}, "web": {"output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/illustration.png", "resizeMode": "contain", "backgroundColor": "#5B7FE5"}]], "experiments": {"typedRoutes": true, "reactCompiler": true}}}